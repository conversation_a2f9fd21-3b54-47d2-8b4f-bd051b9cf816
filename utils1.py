import os
import uuid
import requests
import shutil
from datetime import datetime
from masks_process_http import parent_dir
from flask import jsonify

def create_downloadurl(uuid, file_name):
    # 读取 config/ip.txt 文件中的 context 值
    ip_file_path = os.path.join(parent_dir, 'config/ip.txt')
    try:
        with open(ip_file_path, 'r', encoding='utf-8') as f:
            context = f.read().strip()
    except Exception as e:
        print(f"读取 IP 文件失败: {e}")
        return jsonify({"error": "读取 IP 文件失败"}), 500

    file_url_result = context + uuid + "/" + file_name
    return file_url_result

# 生成简化的uuid字符串用于文件名
def get_uuid_name():
    # 获取精确到微秒的时间戳字符串（16位）
    timestamp_str = datetime.now().strftime("%Y%m%d%H%M%S%f")
    # 取UUID的前8位
    short_uuid = str(uuid.uuid4())[:8]
    # 拼接时间戳和短UUID
    unique_id = f"{timestamp_str}{short_uuid}"
    return unique_id


# 从指定url获取文件,并将文件使用get_uuid_name()更改名称后保存在pdf目录下
def get_file_from_url(url: str, type: str) -> tuple[str, str]:
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    uuid_str = get_uuid_name()
    if type == 'a':
        # 构建年度文件路径
        file_dir = os.path.join(current_dir, 'data/annual_tasks', uuid_str)
    elif type == 'm':
        file_dir = os.path.join(current_dir, 'data/monthly_plan', uuid_str)
    elif type == 'r':
        file_dir = os.path.join(current_dir, 'data/reports/monthly', uuid_str)
    # 如果目录不存在，则创建
    if not os.path.exists(file_dir):
        os.makedirs(file_dir)

    # 发送HTTP GET请求下载文件
    response = requests.get(url)

    # 获取原始文件扩展名
    # ext = os.path.splitext(url)[1]
    # 使用get_uuid_name()生成新文件名，并保留原始扩展名
    unique_filename = uuid_str + '.xlsx'
    # 构建文件保存路径
    file_path = os.path.join(file_dir, unique_filename)

    # 写入文件
    with open(file_path, 'wb') as f:
        f.write(response.content)

    print(f"文件已保存至：{file_path}")
    return file_path, uuid_str

def delete_folder_contents(folder_path):
    # 检查路径是否存在
    if os.path.exists(folder_path):
        # 删除文件夹下的所有内容，但不删除文件夹本身
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)  # 删除文件
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)  # 删除文件夹及其内容
            except Exception as e:
                print(f'Failed to delete {file_path}. Reason: {e}')
    else:
        print("The directory does not exist")

